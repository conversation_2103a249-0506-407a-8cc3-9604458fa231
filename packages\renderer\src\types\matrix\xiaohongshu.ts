import { PaginationParams } from '@app/shared/types/database.types'

/**
 * 小红书平台相关类型定义
 */

/**
 * 小红书账号信息
 */
export interface XhsAccount {
  id: number
  nickname?: string
  avatar?: string
  openId?: string
  accessTokenStatus?: number
  createTime?: number
  expiredTime?: number
  fanNum?: number
  likeNum?: number
  publishNum?: number
  publishStatus?: number
  remark?: string
  province?: string
  city?: string
}

/**
 * 小红书账号搜索参数
 */
export interface XhsAccountSearchParams extends PaginationParams {
  createTime?: number[]
  province?: string
  search?: string
}

/**
 * 小红书发布参数
 */
export interface XhsCreateMatrixParams {
  accountIds: number[]
  name: string
  titles: string[]
  videoList: XhsVideoList[]
  totalAccount: number
  totalVideo: number
  timeType: number
  timeSetting?: XhsTimeSetting
  marketingTarget?: string
}

/**
 * 小红书视频信息
 */
export interface XhsVideoList {
  cover: string
  url?: string
  name?: string
}

/**
 * 小红书时间设置
 */
export interface XhsTimeSetting {
  publishTime?: number
  period?: number
  loopDays?: number[]
  loopTime?: string
  loopPeriod?: number
  numEachDay?: number
}

/**
 * 小红书发布详情
 */
export interface XhsPublishDetail {
  id?: number
  accountId?: number
  planId?: number
  title?: string
  description?: string
  url?: string
  videoCover?: string
  status?: number
  createTime?: number
  nickname?: string
  error?: string
}

/**
 * 小红书推送计划
 */
export interface XhsPushPlan {
  id?: number
  name?: string
  status?: number
  totalAccount?: number
  totalVideo?: number
  success?: number
  fail?: number
  wait?: number
  doing?: number
  createTime?: number
  publishTime?: number
}

/**
 * 小红书账号概览
 */
export interface XhsAccountOverview {
  authAccount: number
  fans: number
  changeFans: number
  totalVideo: number
  playNum: number
  likeNum: number
  commentNum: number
  shareNum: number
}

/**
 * 小红书时间范围参数
 */
export interface XhsTimeRangeParams {
  startTime: number
  endTime: number
}

/**
 * 小红书发布表单详情
 */
export interface XhsPublishFormDetail {
  id: number
  name: string
  channelType: number
  publishType: number
  totalVideo: number
  totalAccount: number
  marketingTarget: string
  accountIds: number[]
  accountList: XhsAccount[]
  videoList: XhsVideoDetailList[]
  titles: string[]
  timeType: number
  timeSetting: XhsTimeSetting
  createTime: number
  updateTime: number
}

/**
 * 小红书视频详情列表
 */
export interface XhsVideoDetailList {
  url: string
  name?: string
  videoId?: string
  duration?: number
  size?: number
  cover: string
  title?: string
}
