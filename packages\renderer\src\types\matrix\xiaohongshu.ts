import { PaginationParams } from '@app/shared/types/database.types'

/**
 * 小红书账号搜索参数
 */
export interface AccountSearchParams extends PaginationParams {
  createTime?: number[];
  province?: string;
  search?: string;
}

/**
 * 小红书账号信息
 */
export interface Account {
  accessTokenStatus?: number;
  accessTokenStatusOrigin?: number;
  avatar?: string;
  bindPhone?: null;
  channelType?: number;
  city?: string;
  commentNum?: null;
  createTime?: number;
  expiredTime?: number;
  fanNum?: null;
  groupList?: GroupList[];
  id: number;
  likeNum?: null;
  nickname?: string;
  openId?: string;
  orgAccountType?: number;
  orgNickname?: string;
  orgSessionId?: string;
  province?: string;
  publishNum?: number;
  publishStatus?: number;
  remark?: string;
  sessionId?: string;
  shareNum?: null;
}

/**
 * 小红书账号分组信息
 */
export interface GroupList {
  accountId?: number;
  groupId?: number;
  name?: string;
}

/**
 * 小红书账号推送详情请求参数
 */
export interface AccountPushDetailRequestParams extends PaginationParams {
  accountId?: number;
  createTime?: number[];
  planIds?: number[];
  status?: number;
  search?: string;
}

/**
 * 小红书账号推送详情
 */
export interface AccountPushDetail {
  accountId?: number;
  createTime?: number;
  description?: string;
  error?: null;
  finalStatus?: number;
  id?: number;
  nickname?: string;
  planId?: number;
  status?: number;
  title?: string;
  url?: string;
  videoCover?: string;
  videoDuration?: number;
  videoId?: number;
  videoName?: string;
}

/**
 * 小红书时间范围参数
 */
export interface TimeRangeParams {
  startTime: number;
  endTime: number;
}

/**
 * 小红书账号概览数据
 */
export interface XhsAccountOverview {
  authAccount: number;
  changeFans: number;
  commentNum: number;
  likeNum: number;
  fans: number;
  readNum: number;
  shareNum: number;
  totalNote: number;
}

/**
 * 小红书发布计划
 */
export interface XhsPushPlan {
  business?: null;
  channelType?: null;
  createTime?: number;
  detailVOList?: XhsPushPlanDetail[];
  doing?: number;
  fail?: number;
  id?: number;
  marketingTarget?: null;
  name?: string;
  publishTime?: number;
  publishType?: null;
  status?: number;
  success?: number;
  totalAccount?: number;
  totalNote?: number;
  wait?: number;
}

/**
 * 小红书发布计划详情
 */
export interface XhsPushPlanDetail {
  accountId?: number;
  createTime?: number;
  description?: string;
  error?: null;
  finalStatus?: null;
  id?: number;
  nickname?: null;
  planId?: number;
  status?: number;
  title?: string;
  url?: string;
  videoCover?: string;
  videoDuration?: number;
  videoId?: number;
  videoName?: string;
}

/**
 * 小红书发布表单详情
 */
export interface XhsPublishFormDetail {
  id: number;
  name: string;
  channelType: number;
  publishType: number;
  deliverType: number;
  totalNote: number;
  totalAccount: number;
  marketingTarget: string;
  accountIds: number[];
  accountList: Account[];
  noteList: NoteDetailList[];
  titles: string[];
  businessId: any;
  setting: number;
  timeType: number;
  timeSetting: TimeSetting;
  createTime: number;
  updateTime: number;
}

/**
 * 小红书笔记详情列表
 */
export interface NoteDetailList {
  url: string;
  name: any;
  noteId: any;
  orgTitle: any;
  duration: any;
  size: any;
  cover: string;
  orgCover?: string;
  title: any;
}

/**
 * 小红书定时设置
 */
export interface TimeSetting {
  loopDays: number[];
  loopPeriod: number;
  loopTime: string;
  numEachDay: number;
  period: number;
  periodType: number[];
  publishTime: number;
}

/**
 * 小红书创建发布参数
 */
export interface CreateMatrixParams {
  accountIds: number[];
  detailDOS: DetailDOS[];
  marketingTarget: string;
  name: string;
  setting: number;
  timeSetting: TimeSetting;
  timeType: number;
  titles: string[];
  totalAccount: number;
  totalNote: number;
  noteList: NoteList[];
}

/**
 * 小红书任务明细
 */
export interface DetailDOS {
  accountId: string;
  cover: string;
  description: string;
  title: string;
  url: string;
}

/**
 * 小红书笔记列表
 */
export interface NoteList {
  cover: string;
  url?: string;
  name?: string;
  orgCover?: string;
}
