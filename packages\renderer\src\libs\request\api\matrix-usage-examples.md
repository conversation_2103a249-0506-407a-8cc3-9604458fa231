# MatrixModule 泛型接口使用示例

## 概述

`MatrixModule` 已按照 `AiModule` 的设计模式进行重构，采用泛型设计来支持多平台（抖音、小红书）的统一接口。

## 设计模式特点

1. **命名空间模式**：使用 `export namespace MatrixModule` 包装所有相关类型和接口
2. **泛型设计**：
   - 定义了 `Platforms` 联合类型作为平台标识
   - 使用条件类型根据平台类型映射对应的参数和返回值类型
   - 提供类型安全的平台特定接口
3. **向后兼容**：保留旧接口并标记为 `@deprecated`，便于渐进式迁移

## 新接口使用示例

### 1. 账号管理接口

```typescript
import { MatrixModule } from '@/libs/request/api/matrix'

// 获取抖音账号列表
const dyAccounts = await MatrixModule.account.list('douyin', {
  pageNum: 1,
  pageSize: 10,
  province: '北京'
})

// 获取小红书账号列表
const xhsAccounts = await MatrixModule.account.list('xiaohongshu', {
  pageNum: 1,
  pageSize: 10,
  search: '关键词'
})

// 获取账号概览数据（仅抖音支持）
const overview = await MatrixModule.account.overview('douyin', {
  startTime: Date.now() - 7 * 24 * 60 * 60 * 1000,
  endTime: Date.now()
})

// 获取授权码（仅抖音支持）
const authCode = await MatrixModule.account.getAuthCode('douyin')
```

### 2. 发布管理接口

```typescript
// 保存草稿
const draftData: MatrixModule.CreateMatrixParamsByPlatform<'douyin'> = {
  accountIds: [1, 2, 3],
  name: '测试发布计划',
  // ... 其他参数
}

await MatrixModule.publish.saveDraft('douyin', draftData)

// 创建发布计划
await MatrixModule.publish.create('douyin', draftData)

// 获取发布计划列表
const plans = await MatrixModule.publish.planList('douyin', {
  pageNum: 1,
  pageSize: 10
})

// 获取草稿列表
const drafts = await MatrixModule.publish.draftList('douyin')
```

### 3. 分组管理接口

```typescript
// 创建分组
await MatrixModule.group.create({
  name: '新分组',
  tags: '标签1,标签2'
})

// 获取分组列表
const groups = await MatrixModule.group.list()

// 添加账号到分组
await MatrixModule.group.addAccounts({
  groupId: 1,
  accountIds: [1, 2, 3]
})
```

## 类型安全特性

### 1. 平台特定类型推断

```typescript
// TypeScript 会自动推断返回类型
const dyAccounts = await MatrixModule.account.list('douyin', params)
// dyAccounts 的类型为 DyAccount[]

const xhsAccounts = await MatrixModule.account.list('xiaohongshu', params)
// xhsAccounts 的类型为 XhsAccount[]
```

### 2. 参数类型验证

```typescript
// 编译时类型检查，确保参数类型正确
const params: MatrixModule.AccountSearchParamsByPlatform<'douyin'> = {
  pageNum: 1,
  pageSize: 10,
  province: '北京' // 抖音特有参数
}

const xhsParams: MatrixModule.AccountSearchParamsByPlatform<'xiaohongshu'> = {
  pageNum: 1,
  pageSize: 10,
  search: '关键词' // 小红书特有参数
}
```

## 迁移指南

### 从旧接口迁移到新接口

```typescript
// 旧接口（已废弃）
const accounts = await MatrixModule.dyAccount.list(params)
const overview = await MatrixModule.dyAccount.overview(params)

// 新接口
const accounts = await MatrixModule.account.list('douyin', params)
const overview = await MatrixModule.account.overview('douyin', params)
```

### React Hook 中的使用

```typescript
import { useQuery } from '@tanstack/react-query'
import { MatrixModule } from '@/libs/request/api/matrix'

// 使用新接口
const { data: dyAccounts } = useQuery({
  queryKey: ['accounts', 'douyin'],
  queryFn: () => MatrixModule.account.list('douyin', { pageNum: 1, pageSize: 10 })
})

const { data: xhsAccounts } = useQuery({
  queryKey: ['accounts', 'xiaohongshu'],
  queryFn: () => MatrixModule.account.list('xiaohongshu', { pageNum: 1, pageSize: 10 })
})
```

## 扩展新平台

要添加新平台支持，只需：

1. 在 `Platforms` 类型中添加新平台
2. 在各个条件类型中添加新平台的类型映射
3. 在接口实现中添加新平台的处理逻辑
4. 创建对应的类型定义文件

```typescript
// 1. 添加新平台类型
export type Platforms = 'douyin' | 'xiaohongshu' | 'kuaishou'

// 2. 添加类型映射
export type AccountByPlatform<TPlatform extends Platforms> = 
  TPlatform extends 'douyin' ? DyAccount
  : TPlatform extends 'xiaohongshu' ? XhsAccount
  : TPlatform extends 'kuaishou' ? KsAccount
  : never

// 3. 添加接口实现
list<TPlatform extends Platforms>(platform: TPlatform, params: AccountSearchParamsByPlatform<TPlatform>) {
  if (platform === 'douyin') {
    return fetchPagination<AccountByPlatform<TPlatform>>('/app-api/publishing/dy-account/page', params)
  } else if (platform === 'xiaohongshu') {
    return request.get<AccountByPlatform<TPlatform>[]>('/app-api/publishing/xhs-account/page', params)
  } else if (platform === 'kuaishou') {
    return fetchPagination<AccountByPlatform<TPlatform>>('/app-api/publishing/ks-account/page', params)
  }
  throw new Error(`Unsupported platform: ${platform}`)
}
```
