import { PaginationParams } from '@app/shared/types/database.types'

/**
 * 快手平台相关类型定义
 */

/**
 * 快手账号信息
 */
export interface KsAccount {
  id: number
  nickname?: string
  avatar?: string
  openId?: string
  accessTokenStatus?: number
  createTime?: number
  expiredTime?: number
  fanNum?: number
  likeNum?: number
  publishNum?: number
  publishStatus?: number
  remark?: string
  province?: string
  city?: string
}

/**
 * 快手账号搜索参数
 */
export interface KsAccountSearchParams extends PaginationParams {
  createTime?: number[]
  province?: string
  search?: string
}

/**
 * 快手发布参数
 */
export interface KsCreateMatrixParams {
  accountIds: number[]
  name: string
  titles: string[]
  videoList: KsVideoList[]
  totalAccount: number
  totalVideo: number
  timeType: number
  timeSetting?: KsTimeSetting
  marketingTarget?: string
}

/**
 * 快手视频信息
 */
export interface KsVideoList {
  cover: string
  url?: string
  name?: string
}

/**
 * 快手时间设置
 */
export interface KsTimeSetting {
  publishTime?: number
  period?: number
  loopDays?: number[]
  loopTime?: string
  loopPeriod?: number
  numEachDay?: number
}

/**
 * 快手发布详情
 */
export interface KsPublishDetail {
  id?: number
  accountId?: number
  planId?: number
  title?: string
  description?: string
  url?: string
  videoCover?: string
  status?: number
  createTime?: number
  nickname?: string
  error?: string
}

/**
 * 快手推送计划
 */
export interface KsPushPlan {
  id?: number
  name?: string
  status?: number
  totalAccount?: number
  totalVideo?: number
  success?: number
  fail?: number
  wait?: number
  doing?: number
  createTime?: number
  publishTime?: number
}

/**
 * 快手账号概览
 */
export interface KsAccountOverview {
  authAccount: number
  fans: number
  changeFans: number
  totalVideo: number
  playNum: number
  likeNum: number
  commentNum: number
  shareNum: number
}

/**
 * 快手时间范围参数
 */
export interface KsTimeRangeParams {
  startTime: number
  endTime: number
}

/**
 * 快手发布表单详情
 */
export interface KsPublishFormDetail {
  id: number
  name: string
  channelType: number
  publishType: number
  totalVideo: number
  totalAccount: number
  marketingTarget: string
  accountIds: number[]
  accountList: KsAccount[]
  videoList: KsVideoDetailList[]
  titles: string[]
  timeType: number
  timeSetting: KsTimeSetting
  createTime: number
  updateTime: number
}

/**
 * 快手视频详情列表
 */
export interface KsVideoDetailList {
  url: string
  name?: string
  videoId?: string
  duration?: number
  size?: number
  cover: string
  title?: string
}
