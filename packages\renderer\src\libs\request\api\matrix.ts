import request, { fetchPagination } from '../request'
import { PaginationParams } from '@app/shared/types/database.types'
import {
  Account, AccountPushDetail, AccountPushDetailRequestParams,
  AccountSearchParams,
  CreateGroup, CreateMatrixParams, DyAccountOverview, DyPublishFormDetail, DyPushPlan,
  GroupAccount,
  GroupAccountSearchParams,
  GroupItem, TimeRangeParams,
  UpdateGroup
} from '@/types/matrix/douyin'
import {
  XhsAccount, XhsAccountSearchParams, XhsCreateMatrixParams,
  XhsAccountOverview, XhsPublishFormDetail, XhsPushPlan,
  XhsTimeRangeParams
} from '@/types/matrix/xiaohongshu'
import {
  KsAccount, KsAccountSearchParams, KsCreateMatrixParams,
  KsAccountOverview, KsPublishFormDetail, KsPushPlan,
  KsTimeRangeParams
} from '@/types/matrix/kuaishou'

namespace MatrixModuleInternal {

  // 平台类型定义
  export type Platform = 'douyin' | 'xiaohongshu' | 'kuaishou'

  // 平台路径映射
  export type PlatformPath<TPlatform extends Platform> = TPlatform extends 'douyin'
    ? 'dy'
    : TPlatform extends 'xiaohongshu'
      ? 'xhs'
      : TPlatform extends 'kuaishou'
        ? 'ks'
        : never

  // 账号相关操作类型
  export type AccountOperation =
    | 'list'
    | 'overview'
    | 'pushDetail'
    | 'planList'
    | 'getCode'

  // 发布相关操作类型
  export type PublishOperation =
    | 'pushPlanList'
    | 'saveDraft'
    | 'draftList'
    | 'publish'
    | 'deleteDraft'
    | 'batchDeleteDraft'
    | 'taskDetail'
    | 'draftDetail'

  // 分组相关操作类型
  export type GroupOperation =
    | 'create'
    | 'update'
    | 'delete'
    | 'list'
    | 'account'
    | 'addAccount'
    | 'deleteAccount'

  // 根据平台和操作类型映射请求参数
  export type AccountOperationParams<TPlatform extends Platform, TOperation extends AccountOperation> =
    TPlatform extends 'douyin'
      ? TOperation extends 'list'
        ? AccountSearchParams
        : TOperation extends 'overview'
          ? TimeRangeParams | undefined
          : TOperation extends 'pushDetail'
            ? AccountPushDetailRequestParams
            : TOperation extends 'planList'
              ? { accountIds: number[], limit: number }
              : TOperation extends 'getCode'
                ? void
                : never
      : TPlatform extends 'xiaohongshu'
        ? TOperation extends 'list'
          ? XhsAccountSearchParams
          : TOperation extends 'overview'
            ? XhsTimeRangeParams | undefined
            : never
        : TPlatform extends 'kuaishou'
          ? TOperation extends 'list'
            ? KsAccountSearchParams
            : TOperation extends 'overview'
              ? KsTimeRangeParams | undefined
              : never
          : never

  // 根据平台和操作类型映射返回结果
  export type AccountOperationResult<TPlatform extends Platform, TOperation extends AccountOperation> =
    TPlatform extends 'douyin'
      ? TOperation extends 'list'
        ? Account[]
        : TOperation extends 'overview'
          ? DyAccountOverview
          : TOperation extends 'pushDetail'
            ? AccountPushDetail[]
            : TOperation extends 'planList'
              ? { id: number, name: string }[]
              : TOperation extends 'getCode'
                ? string
                : never
      : TPlatform extends 'xiaohongshu'
        ? TOperation extends 'list'
          ? XhsAccount[]
          : TOperation extends 'overview'
            ? XhsAccountOverview
            : never
        : TPlatform extends 'kuaishou'
          ? TOperation extends 'list'
            ? KsAccount[]
            : TOperation extends 'overview'
              ? KsAccountOverview
              : never
          : never

  // 发布操作参数映射
  export type PublishOperationParams<TOperation extends PublishOperation> =
    TOperation extends 'pushPlanList'
      ? PaginationParams
      : TOperation extends 'saveDraft'
        ? CreateMatrixParams & { id?: number }
        : TOperation extends 'draftList'
          ? void
          : TOperation extends 'publish'
            ? CreateMatrixParams
            : TOperation extends 'deleteDraft'
              ? number
              : TOperation extends 'batchDeleteDraft'
                ? { ids: number[] }
                : TOperation extends 'taskDetail' | 'draftDetail'
                  ? number
                  : never

  // 发布操作结果映射
  export type PublishOperationResult<TOperation extends PublishOperation> =
    TOperation extends 'pushPlanList'
      ? DyPushPlan[]
      : TOperation extends 'saveDraft'
        ? void
        : TOperation extends 'draftList'
          ? (CreateMatrixParams & { id: number })[]
          : TOperation extends 'publish'
            ? void
            : TOperation extends 'deleteDraft' | 'batchDeleteDraft'
              ? void
              : TOperation extends 'taskDetail' | 'draftDetail'
                ? DyPublishFormDetail
                : never

  // 分组操作参数映射
  export type GroupOperationParams<TOperation extends GroupOperation> =
    TOperation extends 'create'
      ? CreateGroup
      : TOperation extends 'update'
        ? UpdateGroup
        : TOperation extends 'delete'
          ? number
          : TOperation extends 'list'
            ? void
            : TOperation extends 'account'
              ? GroupAccountSearchParams
              : TOperation extends 'addAccount' | 'deleteAccount'
                ? { groupId: number, accountIds: number[] }
                : never

  // 分组操作结果映射
  export type GroupOperationResult<TOperation extends GroupOperation> =
    TOperation extends 'create' | 'update' | 'delete' | 'addAccount' | 'deleteAccount'
      ? void
      : TOperation extends 'list'
        ? GroupItem[]
        : TOperation extends 'account'
          ? GroupAccount[]
          : never

  // 抖音平台接口模块
  export const douyin = {
    // 分组管理
    group: {
      create(data: GroupOperationParams<'create'>) {
        return request.post('/app-api/publishing/dy-group/add', data)
      },
      update(data: GroupOperationParams<'update'>) {
        return request.put('/app-api/publishing/dy-group/update', data)
      },
      delete(id: GroupOperationParams<'delete'>) {
        return request.delete(`/app-api/publishing/dy-group/delete?id=${id}`)
      },
      list() {
        return request.get<GroupOperationResult<'list'>>('/app-api/publishing/dy-group/list')
      },
      account(data: GroupOperationParams<'account'>) {
        return fetchPagination<GroupAccount>('/app-api/publishing/dy-group/account-page', data)
      },
      addAccount(data: GroupOperationParams<'addAccount'>) {
        return request.post('/app-api/publishing/dy-group/add-account', data)
      },
      deleteAccount(data: GroupOperationParams<'deleteAccount'>) {
        return request.post('/app-api/publishing/dy-group/delete-account', data)
      },
    },

    // 账号管理
    account: {
      list(params: AccountOperationParams<'douyin', 'list'>) {
        return fetchPagination<Account>('/app-api/publishing/dy-account/page', params)
      },
      overview(data?: AccountOperationParams<'douyin', 'overview'>) {
        return request.post<AccountOperationResult<'douyin', 'overview'>>('/app-api/publishing/dy-account/overview', data)
      },
      pushDetail(data: AccountOperationParams<'douyin', 'pushDetail'>) {
        return fetchPagination<AccountPushDetail>('/app-api/publishing/dy-push-detail/page', data)
      },
      planList(data: AccountOperationParams<'douyin', 'planList'>) {
        return request.post<AccountOperationResult<'douyin', 'planList'>>('/app-api/publishing/dy-push-plan/list-plan', data)
      },
      getCode() {
        return request.get<AccountOperationResult<'douyin', 'getCode'>>('/app-api/publishing/dy-auth/get-code')
      },
    },

    // 发布管理
    publish: {
      pushPlanList(data: PublishOperationParams<'pushPlanList'>) {
        return fetchPagination<DyPushPlan>('/app-api/publishing/dy-push-plan/page', data)
      },
      saveDraft(data: PublishOperationParams<'saveDraft'>) {
        return request.post('/app-api/publishing/dy-push-draft/save', data)
      },
      draftList() {
        return request.get<PublishOperationResult<'draftList'>>('/app-api/publishing/dy-push-draft/list')
      },
      publish(data: PublishOperationParams<'publish'>) {
        return request.post('/app-api/publishing/dy-push-plan/create', data)
      },
      deleteDraft(id: PublishOperationParams<'deleteDraft'>) {
        return request.delete(`/app-api/publishing/dy-push-draft/delete?id=${id}`)
      },
      batchDeleteDraft(params: PublishOperationParams<'batchDeleteDraft'>) {
        return request.delete(`/app-api/publishing/dy-push-draft/delete-list?ids=${params.ids}`)
      },
      taskDetail(id: PublishOperationParams<'taskDetail'>) {
        return request.get<PublishOperationResult<'taskDetail'>>(`/app-api/publishing/dy-push-plan/detail?id=${id}`)
      },
      draftDetail(id: PublishOperationParams<'draftDetail'>) {
        return request.get<PublishOperationResult<'draftDetail'>>(`/app-api/publishing/dy-push-draft/detail?id=${id}`)
      },
    },
  }

  // 小红书平台接口模块
  export const xiaohongshu = {
    // 账号管理
    account: {
      list(params: AccountOperationParams<'xiaohongshu', 'list'>) {
        return fetchPagination<XhsAccount>('/app-api/publishing/xhs-account/page', params)
      },
      overview(data?: AccountOperationParams<'xiaohongshu', 'overview'>) {
        return request.post<AccountOperationResult<'xiaohongshu', 'overview'>>('/app-api/publishing/xhs-account/overview', data)
      },
    },

    // 发布管理
    publish: {
      saveDraft(data: XhsCreateMatrixParams & { id?: number }) {
        return request.post('/app-api/publishing/xhs-push-draft/save', data)
      },
      draftList() {
        return request.get<(XhsCreateMatrixParams & { id: number })[]>('/app-api/publishing/xhs-push-draft/list')
      },
      publish(data: XhsCreateMatrixParams) {
        return request.post('/app-api/publishing/xhs-push-plan/create', data)
      },
      pushPlanList(data: PaginationParams) {
        return fetchPagination<XhsPushPlan>('/app-api/publishing/xhs-push-plan/page', data)
      },
      taskDetail(id: number) {
        return request.get<XhsPublishFormDetail>(`/app-api/publishing/xhs-push-plan/detail?id=${id}`)
      },
      draftDetail(id: number) {
        return request.get<XhsPublishFormDetail>(`/app-api/publishing/xhs-push-draft/detail?id=${id}`)
      },
    },
  }

  // 快手平台接口模块
  export const kuaishou = {
    // 账号管理
    account: {
      list(params: AccountOperationParams<'kuaishou', 'list'>) {
        return fetchPagination<KsAccount>('/app-api/publishing/ks-account/page', params)
      },
      overview(data?: AccountOperationParams<'kuaishou', 'overview'>) {
        return request.post<AccountOperationResult<'kuaishou', 'overview'>>('/app-api/publishing/ks-account/overview', data)
      },
    },

    // 发布管理
    publish: {
      saveDraft(data: KsCreateMatrixParams & { id?: number }) {
        return request.post('/app-api/publishing/ks-push-draft/save', data)
      },
      draftList() {
        return request.get<(KsCreateMatrixParams & { id: number })[]>('/app-api/publishing/ks-push-draft/list')
      },
      publish(data: KsCreateMatrixParams) {
        return request.post('/app-api/publishing/ks-push-plan/create', data)
      },
      pushPlanList(data: PaginationParams) {
        return fetchPagination<KsPushPlan>('/app-api/publishing/ks-push-plan/page', data)
      },
      taskDetail(id: number) {
        return request.get<KsPublishFormDetail>(`/app-api/publishing/ks-push-plan/detail?id=${id}`)
      },
      draftDetail(id: number) {
        return request.get<KsPublishFormDetail>(`/app-api/publishing/ks-push-draft/detail?id=${id}`)
      },
    },
  }

  // 统一的 endpoints 对象，提供向后兼容性
  export const endpoints = {
    // 分组管理（抖音）
    createGroup: douyin.group.create,
    updateGroup: douyin.group.update,
    deleteGroup: douyin.group.delete,
    groupList: douyin.group.list,
    groupAccount: douyin.group.account,
    addAccountToGroup: douyin.group.addAccount,
    deleteAccountFromGroup: douyin.group.deleteAccount,

    // 抖音账号管理
    dyAccount: {
      list: douyin.account.list,
      accountPushDetail: douyin.account.pushDetail,
      accountPlanList: douyin.account.planList,
      getCode: douyin.account.getCode,
      overview: douyin.account.overview,
      pushPlanList: douyin.publish.pushPlanList,
      saveDraft: douyin.publish.saveDraft,
      draftList: douyin.publish.draftList,
      publish: douyin.publish.publish,
      deleteDraft: douyin.publish.deleteDraft,
      batchDeleteDraft: douyin.publish.batchDeleteDraft,
      taskDetail: douyin.publish.taskDetail,
      draftDetail: douyin.publish.draftDetail,
    },

    // 小红书账号管理
    xhsAccount: {
      list: xiaohongshu.account.list,
      overview: xiaohongshu.account.overview,
      saveDraft: xiaohongshu.publish.saveDraft,
      draftList: xiaohongshu.publish.draftList,
      publish: xiaohongshu.publish.publish,
      pushPlanList: xiaohongshu.publish.pushPlanList,
      taskDetail: xiaohongshu.publish.taskDetail,
      draftDetail: xiaohongshu.publish.draftDetail,
    },

    // 快手账号管理
    ksAccount: {
      list: kuaishou.account.list,
      overview: kuaishou.account.overview,
      saveDraft: kuaishou.publish.saveDraft,
      draftList: kuaishou.publish.draftList,
      publish: kuaishou.publish.publish,
      pushPlanList: kuaishou.publish.pushPlanList,
      taskDetail: kuaishou.publish.taskDetail,
      draftDetail: kuaishou.publish.draftDetail,
    },
  }
}

// 为了向后兼容，导出原有的 MatrixModule 结构
export const MatrixModule = MatrixModuleInternal.endpoints

// 同时导出命名空间以供类型使用
export namespace MatrixModule {
  export type Platform = MatrixModuleInternal.Platform
  export type PlatformPath<TPlatform extends Platform> = MatrixModuleInternal.PlatformPath<TPlatform>
  export type AccountOperation = MatrixModuleInternal.AccountOperation
  export type PublishOperation = MatrixModuleInternal.PublishOperation
  export type GroupOperation = MatrixModuleInternal.GroupOperation
  export type AccountOperationParams<TPlatform extends Platform, TOperation extends AccountOperation> = MatrixModuleInternal.AccountOperationParams<TPlatform, TOperation>
  export type AccountOperationResult<TPlatform extends Platform, TOperation extends AccountOperation> = MatrixModuleInternal.AccountOperationResult<TPlatform, TOperation>
  export type PublishOperationParams<TOperation extends PublishOperation> = MatrixModuleInternal.PublishOperationParams<TOperation>
  export type PublishOperationResult<TOperation extends PublishOperation> = MatrixModuleInternal.PublishOperationResult<TOperation>
  export type GroupOperationParams<TOperation extends GroupOperation> = MatrixModuleInternal.GroupOperationParams<TOperation>
  export type GroupOperationResult<TOperation extends GroupOperation> = MatrixModuleInternal.GroupOperationResult<TOperation>
}
