import request, { fetchPagination } from '../request'
import { PaginationParams } from '@app/shared/types/database.types'
import {
  Account as DyAccount, AccountPushDetail as DyAccountPushDetail, AccountPushDetailRequestParams as DyAccountPushDetailRequestParams,
  AccountSearchParams as DyAccountSearchParams,
  CreateGroup, CreateMatrixParams as DyCreateMatrixParams, DyAccountOverview, DyPublishFormDetail, DyPushPlan,
  GroupAccount,
  GroupAccountSearchParams,
  GroupItem, TimeRangeParams,
  UpdateGroup
} from '@/types/matrix/douyin'
import {
  Account as XhsAccount,
  AccountSearchParams as XhsAccountSearchParams,
  CreateMatrixParams as XhsCreateMatrixParams,
  AccountPushDetail as XhsAccountPushDetail,
  AccountPushDetailRequestParams as XhsAccountPushDetailRequestParams
} from '@/types/matrix/xiaohongshu'

export namespace MatrixModule {

  export type Platforms = 'douyin' | 'xiaohongshu'

  export type AccountByPlatform<TPlatform extends Platforms> = TPlatform extends 'douyin'
    ? DyAccount
    : TPlatform extends 'xiaohongshu'
      ? XhsAccount
      : never

  export type AccountSearchParamsByPlatform<TPlatform extends Platforms> = TPlatform extends 'douyin'
    ? DyAccountSearchParams
    : TPlatform extends 'xiaohongshu'
      ? XhsAccountSearchParams
      : never

  export type CreateMatrixParamsByPlatform<TPlatform extends Platforms> = TPlatform extends 'douyin'
    ? DyCreateMatrixParams
    : TPlatform extends 'xiaohongshu'
      ? XhsCreateMatrixParams
      : never

  export type AccountPushDetailByPlatform<TPlatform extends Platforms> = TPlatform extends 'douyin'
    ? DyAccountPushDetail
    : TPlatform extends 'xiaohongshu'
      ? XhsAccountPushDetail
      : never

  export type AccountPushDetailRequestParamsByPlatform<TPlatform extends Platforms> = TPlatform extends 'douyin'
    ? DyAccountPushDetailRequestParams
    : TPlatform extends 'xiaohongshu'
      ? XhsAccountPushDetailRequestParams
      : never

  // 通用分组管理接口
  export const group = {
    create(data: CreateGroup) {
      return request.post('/app-api/publishing/dy-group/add', data)
    },
    update(data: UpdateGroup) {
      return request.put('/app-api/publishing/dy-group/update', data)
    },
    delete(id: number) {
      return request.delete(`/app-api/publishing/dy-group/delete?id=${id}`)
    },
    list() {
      return request.get<GroupItem[]>('/app-api/publishing/dy-group/list')
    },
    getAccounts(data: GroupAccountSearchParams) {
      return fetchPagination<GroupAccount>('/app-api/publishing/dy-group/account-page', data)
    },
    addAccounts(data: { groupId: number, accountIds: number[] }) {
      return request.post('/app-api/publishing/dy-group/add-account', data)
    },
    removeAccounts(data: { groupId: number, accountIds: number[] }) {
      return request.post('/app-api/publishing/dy-group/delete-account', data)
    },
  }

  // 平台特定的账号管理接口
  export const account = {
    list<TPlatform extends Platforms>(platform: TPlatform, params: AccountSearchParamsByPlatform<TPlatform>) {
      if (platform === 'douyin') {
        return fetchPagination<AccountByPlatform<TPlatform>>('/app-api/publishing/dy-account/page', params)
      } else if (platform === 'xiaohongshu') {
        return request.get<AccountByPlatform<TPlatform>[]>('/app-api/publishing/xhs-account/page', params)
      }
      throw new Error(`Unsupported platform: ${platform}`)
    },

    pushDetail<TPlatform extends Platforms>(platform: TPlatform, data: AccountPushDetailRequestParamsByPlatform<TPlatform>) {
      if (platform === 'douyin') {
        return fetchPagination<AccountPushDetailByPlatform<TPlatform>>('/app-api/publishing/dy-push-detail/page', data)
      }
      throw new Error(`Unsupported platform: ${platform}`)
    },

    planList<TPlatform extends Platforms>(platform: TPlatform, data: { accountIds: number[], limit: number }) {
      if (platform === 'douyin') {
        return request.post<{ id: number, name: string }>('/app-api/publishing/dy-push-plan/list-plan', data)
      }
      throw new Error(`Unsupported platform: ${platform}`)
    },

    getAuthCode<TPlatform extends Platforms>(platform: TPlatform) {
      if (platform === 'douyin') {
        return request.get<string>('/app-api/publishing/dy-auth/get-code')
      }
      throw new Error(`Unsupported platform: ${platform}`)
    },

    overview<TPlatform extends Platforms>(platform: TPlatform, data?: TimeRangeParams) {
      if (platform === 'douyin') {
        return request.post<DyAccountOverview>('/app-api/publishing/dy-account/overview', data)
      }
      throw new Error(`Unsupported platform: ${platform}`)
    },
  }

  // 平台特定的发布管理接口
  export const publish = {
    planList<TPlatform extends Platforms>(platform: TPlatform, data: PaginationParams) {
      if (platform === 'douyin') {
        return fetchPagination<DyPushPlan>('/app-api/publishing/dy-push-plan/page', data)
      }
      throw new Error(`Unsupported platform: ${platform}`)
    },

    saveDraft<TPlatform extends Platforms>(platform: TPlatform, data: CreateMatrixParamsByPlatform<TPlatform> & { id?: number }) {
      if (platform === 'douyin') {
        return request.post('/app-api/publishing/dy-push-draft/save', data)
      }
      throw new Error(`Unsupported platform: ${platform}`)
    },

    draftList<TPlatform extends Platforms>(platform: TPlatform) {
      if (platform === 'douyin') {
        return request.get<(CreateMatrixParamsByPlatform<TPlatform> & { id: number })[]>('/app-api/publishing/dy-push-draft/list')
      }
      throw new Error(`Unsupported platform: ${platform}`)
    },

    create<TPlatform extends Platforms>(platform: TPlatform, data: CreateMatrixParamsByPlatform<TPlatform>) {
      if (platform === 'douyin') {
        return request.post('/app-api/publishing/dy-push-plan/create', data)
      }
      throw new Error(`Unsupported platform: ${platform}`)
    },

    deleteDraft<TPlatform extends Platforms>(platform: TPlatform, id: number) {
      if (platform === 'douyin') {
        return request.delete(`/app-api/publishing/dy-push-draft/delete?id=${id}`)
      }
      throw new Error(`Unsupported platform: ${platform}`)
    },

    batchDeleteDraft<TPlatform extends Platforms>(platform: TPlatform, params: { ids: number[] }) {
      if (platform === 'douyin') {
        return request.delete(`/app-api/publishing/dy-push-draft/delete-list?ids=${params.ids}`)
      }
      throw new Error(`Unsupported platform: ${platform}`)
    },

    taskDetail<TPlatform extends Platforms>(platform: TPlatform, id: number) {
      if (platform === 'douyin') {
        return request.get<DyPublishFormDetail>(`/app-api/publishing/dy-push-plan/detail?id=${id}`)
      }
      throw new Error(`Unsupported platform: ${platform}`)
    },

    draftDetail<TPlatform extends Platforms>(platform: TPlatform, id: number) {
      if (platform === 'douyin') {
        return request.get<DyPublishFormDetail>(`/app-api/publishing/dy-push-draft/detail?id=${id}`)
      }
      throw new Error(`Unsupported platform: ${platform}`)
    },
  }

  // 向后兼容的旧接口（已废弃，建议使用新的泛型接口）
  /** @deprecated 请使用 MatrixModule.group 替代 */
  export const createGroup = group.create
  /** @deprecated 请使用 MatrixModule.group 替代 */
  export const updateGroup = group.update
  /** @deprecated 请使用 MatrixModule.group 替代 */
  export const deleteGroup = group.delete
  /** @deprecated 请使用 MatrixModule.group 替代 */
  export const groupList = group.list
  /** @deprecated 请使用 MatrixModule.group 替代 */
  export const groupAccount = group.getAccounts
  /** @deprecated 请使用 MatrixModule.group 替代 */
  export const addAccountToGroup = group.addAccounts
  /** @deprecated 请使用 MatrixModule.group 替代 */
  export const deleteAccountFromGroup = group.removeAccounts

  /** @deprecated 请使用 MatrixModule.account.list('douyin', params) 替代 */
  export const dyAccount = {
    list: (params: DyAccountSearchParams) => {
      return fetchPagination<DyAccount>('/app-api/publishing/dy-account/page', params)
    },
    accountPushDetail(data: DyAccountPushDetailRequestParams) {
      return fetchPagination<DyAccountPushDetail>('/app-api/publishing/dy-push-detail/page', data)
    },
    accountPlanList(data: { accountIds: number[], limit: number }) {
      return request.post<{ id: number, name: string }>('/app-api/publishing/dy-push-plan/list-plan', data)
    },
    getCode() {
      return request.get<string>('/app-api/publishing/dy-auth/get-code')
    },
    overview(data?: TimeRangeParams) {
      return request.post<DyAccountOverview>('/app-api/publishing/dy-account/overview', data)
    },
    pushPlanList(data: PaginationParams) {
      return fetchPagination<DyPushPlan>('/app-api/publishing/dy-push-plan/page', data)
    },
    saveDraft(data: DyCreateMatrixParams & { id?: number }) {
      return request.post('/app-api/publishing/dy-push-draft/save', data)
    },
    draftList() {
      return request.get<(DyCreateMatrixParams & { id: number })[]>('/app-api/publishing/dy-push-draft/list')
    },
    publish(data: DyCreateMatrixParams) {
      return request.post('/app-api/publishing/dy-push-plan/create', data)
    },
    deleteDraft(id: number) {
      return request.delete(`/app-api/publishing/dy-push-draft/delete?id=${id}`)
    },
    batchDeleteDraft(params: { ids: number[] }) {
      return request.delete(`/app-api/publishing/dy-push-draft/delete-list?ids=${params.ids}`)
    },
    taskDetail(id: number) {
      return request.get<DyPublishFormDetail>(`/app-api/publishing/dy-push-plan/detail?id=${id}`)
    },
    draftDetail(id: number) {
      return request.get<DyPublishFormDetail>(`/app-api/publishing/dy-push-draft/detail?id=${id}`)
    },
  }

  /** @deprecated 请使用 MatrixModule.account.list('xiaohongshu', params) 替代 */
  export const xhsAccount = {
    list(params: XhsAccountSearchParams) {
      return request.get<XhsAccount[]>('/app-api/publishing/xhs-account/page', params)
    }
  }
}
